#!/usr/bin/env python3
"""
AWS Rekognition Face Verification with Pre-recorded Video

This script implements face verification using AWS Rekognition by streaming
a pre-recorded video file through WebSocket connection to AWS Rekognition's
streaming video API.

Backend-only solution that processes video files for face verification
without requiring live camera streams.
"""
import asyncio
import cv2
import json
import sys
import logging
import os
import boto3
from typing import Dict, Any

# --- AWS Configuration ---
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")  # Change to your preferred region
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "YOUR_AWS_ACCESS_KEY")  # Replace with your AWS access key
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "YOUR_AWS_SECRET_KEY")  # Replace with your AWS secret key

# Rekognition Configuration
COLLECTION_ID = "face-verification-collection"  # Your Rekognition collection ID
REFERENCE_FACE_ID = "reference-face-001"  # ID of the reference face to compare against

# Video Configuration
VIDEO_FILE = "person.mp4"  # Your video file path
CHUNK_SIZE = 1024 * 64  # 64KB chunks for streaming
FRAME_RATE = 30  # Frames per second to extract from video

# AWS Rekognition Configuration
# Note: AWS Rekognition doesn't have direct WebSocket streaming for face verification
# We'll simulate streaming by processing video chunks sequentially

# --- End Configuration ---

# Note: Removed complex AWS Event Stream and WebSocket code
# AWS Rekognition face verification works through standard REST API calls

class AWSRekognitionFaceVerifier:
    """Handles face verification using AWS Rekognition with pre-recorded video files."""

    def __init__(
        self,
        video_file: str = VIDEO_FILE,
        collection_id: str = COLLECTION_ID,
        reference_face_id: str = REFERENCE_FACE_ID,
        aws_region: str = AWS_REGION,
        aws_access_key: str = AWS_ACCESS_KEY_ID,
        aws_secret_key: str = AWS_SECRET_ACCESS_KEY,
    ):
        self.video_file = video_file
        self.collection_id = collection_id
        self.reference_face_id = reference_face_id
        self.aws_region = aws_region
        self.aws_access_key = aws_access_key
        self.aws_secret_key = aws_secret_key

        # Initialize AWS Rekognition client
        self.rekognition_client = boto3.client(
            'rekognition',
            region_name=self.aws_region,
            aws_access_key_id=self.aws_access_key,
            aws_secret_access_key=self.aws_secret_key
        )

        # WebSocket connection
        self.websocket = None

        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        self.logger.info("AWS Rekognition Face Verifier initialized")

    def _extract_video_frames(self) -> list:
        """Extract frames from video file for processing."""
        if not os.path.exists(self.video_file):
            raise FileNotFoundError(f"Video file not found: {self.video_file}")

        self.logger.info(f"Extracting frames from video: {self.video_file}")

        cap = cv2.VideoCapture(self.video_file)
        frames = []
        frame_count = 0

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        self.logger.info(f"Video FPS: {fps}, Total frames: {total_frames}")

        # Extract frames at specified frame rate
        frame_interval = max(1, int(fps / FRAME_RATE))

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_count % frame_interval == 0:
                # Convert frame to JPEG bytes
                _, buffer = cv2.imencode('.jpg', frame)
                frame_bytes = buffer.tobytes()
                frames.append(frame_bytes)

            frame_count += 1

        cap.release()
        self.logger.info(f"Extracted {len(frames)} frames for processing")
        return frames

    async def _process_video_chunk(self, frame_data: bytes, frame_number: int) -> Dict[str, Any]:
        """Process a single video frame chunk with AWS Rekognition."""
        try:
            self.logger.debug(f"Processing frame {frame_number}, size: {len(frame_data)} bytes")

            # Use AWS Rekognition to detect and compare faces in this frame
            response = self.rekognition_client.search_faces_by_image(
                CollectionId=self.collection_id,
                Image={'Bytes': frame_data},
                MaxFaces=1,
                FaceMatchThreshold=70.0
            )

            if response['FaceMatches']:
                match = response['FaceMatches'][0]
                return {
                    "success": True,
                    "match_found": True,
                    "confidence": match['Similarity'],
                    "face_id": match['Face']['FaceId'],
                    "frame_number": frame_number
                }
            else:
                return {
                    "success": True,
                    "match_found": False,
                    "frame_number": frame_number
                }

        except Exception as e:
            self.logger.error(f"Failed to process frame {frame_number}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "frame_number": frame_number
            }

    # Note: Removed WebSocket message parsing - not needed for standard AWS Rekognition API

    # Note: All old VFS-related methods have been removed as they're not needed for AWS Rekognition
    # The class now focuses solely on AWS Rekognition face verification functionality




    async def verify_face_with_collection(self) -> Dict[str, Any]:
        """Verify face against a face in AWS Rekognition collection."""
        try:
            self.logger.info("Starting face verification with collection...")

            # Extract frames from video
            frames = self._extract_video_frames()
            if not frames:
                return {"success": False, "error": "No frames extracted from video"}

            # Use the first clear frame for verification
            frame_data = frames[0]

            # Call AWS Rekognition SearchFacesByImage
            response = self.rekognition_client.search_faces_by_image(
                CollectionId=self.collection_id,
                Image={'Bytes': frame_data},
                MaxFaces=1,
                FaceMatchThreshold=80.0
            )

            if response['FaceMatches']:
                match = response['FaceMatches'][0]
                confidence = match['Similarity']
                face_id = match['Face']['FaceId']

                self.logger.info(f"Face match found! Confidence: {confidence}%, Face ID: {face_id}")

                return {
                    "success": True,
                    "confidence": confidence,
                    "face_id": face_id,
                    "match": True
                }
            else:
                self.logger.info("No face match found in collection")
                return {
                    "success": True,
                    "confidence": 0,
                    "match": False
                }

        except Exception as e:
            self.logger.error(f"Face verification failed: {str(e)}")
            return {"success": False, "error": str(e)}

    async def stream_video_for_verification(self) -> Dict[str, Any]:
        """Process video chunks sequentially to simulate streaming verification."""
        try:
            self.logger.info("Starting video chunk processing for face verification...")

            # Extract frames from video
            frames = self._extract_video_frames()
            if not frames:
                return {"success": False, "error": "No frames extracted from video"}

            self.logger.info(f"Processing {len(frames)} video chunks...")

            # Process frames in chunks to simulate streaming
            best_match = None
            best_confidence = 0

            for i, frame_data in enumerate(frames):
                self.logger.info(f"Processing chunk {i+1}/{len(frames)}")

                try:
                    # Use AWS Rekognition to search for faces in this frame
                    response = self.rekognition_client.search_faces_by_image(
                        CollectionId=self.collection_id,
                        Image={'Bytes': frame_data},
                        MaxFaces=1,
                        FaceMatchThreshold=70.0  # Lower threshold for streaming
                    )

                    if response['FaceMatches']:
                        match = response['FaceMatches'][0]
                        confidence = match['Similarity']
                        face_id = match['Face']['FaceId']

                        self.logger.info(f"Frame {i+1}: Face match found with {confidence:.2f}% confidence")

                        # Keep track of best match
                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_match = {
                                "face_id": face_id,
                                "confidence": confidence,
                                "frame_number": i+1
                            }
                    else:
                        self.logger.debug(f"Frame {i+1}: No face match found")

                except Exception as e:
                    self.logger.warning(f"Error processing frame {i+1}: {str(e)}")
                    continue

                # Add small delay to simulate real-time streaming
                await asyncio.sleep(0.1)

            # Return best result
            if best_match:
                self.logger.info(f"✅ Best match found in frame {best_match['frame_number']} with {best_match['confidence']:.2f}% confidence")
                return {
                    "success": True,
                    "match": True,
                    "confidence": best_match['confidence'],
                    "face_id": best_match['face_id'],
                    "frame_number": best_match['frame_number'],
                    "total_frames_processed": len(frames)
                }
            else:
                self.logger.info("❌ No face matches found in any frame")
                return {
                    "success": True,
                    "match": False,
                    "confidence": 0,
                    "total_frames_processed": len(frames)
                }

        except Exception as e:
            self.logger.error(f"Video streaming failed: {str(e)}")
            return {"success": False, "error": str(e)}

    # Note: End-of-stream not needed for standard AWS Rekognition API

    async def run_face_verification(self) -> Dict[str, Any]:
        """Run the complete face verification process."""
        self.logger.info("Starting AWS Rekognition face verification process...")

        try:
            # Method 1: Direct face verification with collection
            self.logger.info("Attempting direct face verification with collection...")
            collection_result = await self.verify_face_with_collection()

            if collection_result["success"]:
                self.logger.info("✅ Face verification completed successfully!")
                return collection_result

            # Method 2: If direct verification fails, try streaming approach
            self.logger.info("Direct verification failed, trying streaming approach...")
            streaming_result = await self.stream_video_for_verification()

            if streaming_result["success"]:
                self.logger.info("✅ Streaming face verification completed successfully!")
                return streaming_result
            else:
                self.logger.error("❌ Both verification methods failed")
                return streaming_result

        except Exception as e:
            self.logger.error(f"Face verification process failed: {str(e)}")
            return {"success": False, "error": str(e)}

    async def create_face_collection(self) -> bool:
        """Create a face collection in AWS Rekognition if it doesn't exist."""
        try:
            # Check if collection exists
            try:
                self.rekognition_client.describe_collection(CollectionId=self.collection_id)
                self.logger.info(f"Collection '{self.collection_id}' already exists")
                return True
            except self.rekognition_client.exceptions.ResourceNotFoundException:
                # Collection doesn't exist, create it
                self.logger.info(f"Creating new collection: {self.collection_id}")
                self.rekognition_client.create_collection(CollectionId=self.collection_id)
                self.logger.info("Collection created successfully")
                return True

        except Exception as e:
            self.logger.error(f"Failed to create collection: {str(e)}")
            return False

    async def add_reference_face(self, reference_image_path: str) -> bool:
        """Add a reference face to the collection."""
        try:
            if not os.path.exists(reference_image_path):
                self.logger.error(f"Reference image not found: {reference_image_path}")
                return False

            with open(reference_image_path, 'rb') as image_file:
                image_bytes = image_file.read()

            response = self.rekognition_client.index_faces(
                CollectionId=self.collection_id,
                Image={'Bytes': image_bytes},
                ExternalImageId=self.reference_face_id,
                MaxFaces=1,
                QualityFilter='AUTO'
            )

            if response['FaceRecords']:
                face_id = response['FaceRecords'][0]['Face']['FaceId']
                self.logger.info(f"Reference face added successfully. Face ID: {face_id}")
                return True
            else:
                self.logger.error("No face detected in reference image")
                return False

        except Exception as e:
            self.logger.error(f"Failed to add reference face: {str(e)}")
            return False

async def run_demo_mode():
    """Run a demo simulation of face verification without real AWS credentials."""
    import random
    import time

    print("\n" + "="*50)
    print("🎭 DEMO MODE - AWS Rekognition Face Verification")
    print("="*50)
    print(f"📹 Video file: {VIDEO_FILE}")
    print(f"🗂️  Collection ID: {COLLECTION_ID}")
    print(f"🆔 Reference Face ID: {REFERENCE_FACE_ID}")
    print("-" * 50)

    # Simulate processing
    print("📁 Setting up face collection...")
    await asyncio.sleep(1)
    print("✅ Face collection ready (simulated)")

    print("🔍 Starting face verification...")
    await asyncio.sleep(2)
    print("📹 Extracting frames from video...")
    await asyncio.sleep(1)
    print("✅ Extracted 25 frames for processing")

    print("🤖 Analyzing faces with AWS Rekognition...")
    await asyncio.sleep(3)

    # Simulate random results
    confidence = random.uniform(85.0, 98.5)
    face_id = f"demo-face-{random.randint(1000, 9999)}"

    print("\n" + "="*50)
    print("🎯 FACE VERIFICATION RESULTS (DEMO)")
    print("="*50)

    if confidence > 90:
        print(f"✅ FACE MATCH FOUND!")
        print(f"🎯 Confidence: {confidence:.2f}%")
        print(f"🆔 Face ID: {face_id}")
        print("✨ This is a simulated result for demonstration purposes")
    else:
        print("❌ NO FACE MATCH FOUND")
        print(f"🎯 Confidence: {confidence:.2f}% (below threshold)")
        print("✨ This is a simulated result for demonstration purposes")

    print("\n💡 To use real AWS Rekognition:")
    print("   1. Get AWS credentials from AWS Console")
    print("   2. Set environment variables or update the script")
    print("   3. Run the script again")

async def main():
    """Main function to run AWS Rekognition face verification."""
    # Check dependencies
    try:
        import crc32c
        import boto3
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please run: pip install boto3 crc32c")
        return

    # Check if video file exists
    if not os.path.exists(VIDEO_FILE):
        print(f"Video file not found: {VIDEO_FILE}")
        print("Please provide a video file for face verification.")
        return

    # Validate AWS credentials
    if AWS_ACCESS_KEY_ID == "YOUR_AWS_ACCESS_KEY" or AWS_SECRET_ACCESS_KEY == "YOUR_AWS_SECRET_KEY":
        print("⚠️  Please update AWS credentials in the configuration section")
        print("Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY with your actual AWS credentials")
        print("\n🔧 Options to provide credentials:")
        print("1. Set environment variables: AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY")
        print("2. Edit the script and replace the placeholder values")
        print("3. Run in demo mode (simulated results)")

        demo_mode = input("\nWould you like to run in demo mode? (y/n): ").lower().strip()
        if demo_mode == 'y':
            print("🎭 Running in DEMO MODE - simulating AWS Rekognition results...")
            await run_demo_mode()
            return
        else:
            return

    print("🚀 Starting AWS Rekognition Face Verification")
    print(f"📹 Video file: {VIDEO_FILE}")
    print(f"🗂️  Collection ID: {COLLECTION_ID}")
    print(f"🆔 Reference Face ID: {REFERENCE_FACE_ID}")
    print("-" * 50)

    # Create and run face verifier
    verifier = AWSRekognitionFaceVerifier()

    try:
        # Create collection if needed
        print("📁 Setting up face collection...")
        if await verifier.create_face_collection():
            print("✅ Face collection ready")
        else:
            print("❌ Failed to setup face collection")
            return

        # Run face verification
        print("🔍 Starting face verification...")
        result = await verifier.run_face_verification()

        # Display results
        print("\n" + "="*50)
        print("🎯 FACE VERIFICATION RESULTS")
        print("="*50)

        if result["success"]:
            if result.get("match"):
                print(f"✅ FACE MATCH FOUND!")
                print(f"🎯 Confidence: {result.get('confidence', 0):.2f}%")
                print(f"🆔 Face ID: {result.get('face_id', 'N/A')}")
            else:
                print("❌ NO FACE MATCH FOUND")
                print("The person in the video does not match any face in the collection")
        else:
            print(f"❌ VERIFICATION FAILED: {result.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Error during verification: {str(e)}")

def create_sample_video():
    """Helper function to create a sample video file for testing."""
    print("📹 Creating sample video file...")

    # Create a simple test video using OpenCV
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(VIDEO_FILE, fourcc, 20.0, (640, 480))

    for i in range(100):  # 5 seconds at 20 FPS
        # Create a simple frame with text
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(frame, f'Test Frame {i}', (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        out.write(frame)

    out.release()
    print(f"✅ Sample video created: {VIDEO_FILE}")

if __name__ == "__main__":
    # Check if video file exists, if not offer to create a sample
    if not os.path.exists(VIDEO_FILE):
        response = input(f"Video file '{VIDEO_FILE}' not found. Create a sample video? (y/n): ")
        if response.lower() == 'y':
            try:
                import numpy as np
                create_sample_video()
            except ImportError:
                print("NumPy is required to create sample video. Please install: pip install numpy")
                sys.exit(1)
        else:
            print("Please provide a video file and try again.")
            sys.exit(1)

    asyncio.run(main())
