#!/usr/bin/env python3
"""
AWS Rekognition Face Verification with Pre-recorded Video

This script implements face verification using AWS Rekognition by streaming
a pre-recorded video file through WebSocket connection to AWS Rekognition's
streaming video API.

Backend-only solution that processes video files for face verification
without requiring live camera streams.
"""
import asyncio
import websockets
import cv2
import json
import sys
import time
import base64
import logging
import os
import struct
import boto3
import hashlib
import hmac
from datetime import datetime
from typing import Optional, Dict, Any
from urllib.parse import urlparse

# --- AWS Configuration ---
AWS_REGION = "us-east-1"  # Change to your preferred region
AWS_ACCESS_KEY_ID = "YOUR_AWS_ACCESS_KEY"  # Replace with your AWS access key
AWS_SECRET_ACCESS_KEY = "YOUR_AWS_SECRET_KEY"  # Replace with your AWS secret key

# Rekognition Configuration
COLLECTION_ID = "face-verification-collection"  # Your Rekognition collection ID
REFERENCE_FACE_ID = "reference-face-001"  # ID of the reference face to compare against

# Video Configuration
VIDEO_FILE = "person.mp4"  # Your video file path
CHUNK_SIZE = 1024 * 64  # 64KB chunks for streaming
FRAME_RATE = 30  # Frames per second to extract from video

# WebSocket Configuration
REKOGNITION_ENDPOINT = f"wss://rekognition.{AWS_REGION}.amazonaws.com/stream-processor"

# --- End Configuration ---

def _construct_aws_event_stream_message(headers: Dict[str, Any], payload: bytes) -> bytes:
    """Construct AWS Event Stream message format for Rekognition WebSocket."""
    header_bytes = b''
    for name, value in headers.items():
        name_len = len(name)
        header_bytes += struct.pack('!B', name_len)
        header_bytes += name.encode('utf-8')
        header_bytes += struct.pack('!B', 7)  # String type
        value_bytes = value.encode('utf-8')
        header_bytes += struct.pack('!H', len(value_bytes))
        header_bytes += value_bytes

    header_length = len(header_bytes)
    total_length = 16 + header_length + len(payload)

    message = b''
    message += struct.pack('!I', total_length)
    message += struct.pack('!I', header_length)

    # Calculate CRC32 for preamble
    import crc32c
    preamble_crc = crc32c.crc32c(message)
    message += struct.pack('!I', preamble_crc)
    message += header_bytes
    message += payload

    # Calculate CRC32 for entire message
    message_crc = crc32c.crc32c(message)
    message += struct.pack('!I', message_crc)
    return message

def _sign_aws_request(method: str, url: str, headers: Dict[str, str], payload: str = "") -> Dict[str, str]:
    """Create AWS Signature Version 4 for WebSocket connection."""
    # Parse URL
    parsed_url = urlparse(url)
    host = parsed_url.netloc

    # Create canonical request
    canonical_uri = parsed_url.path or '/'
    canonical_querystring = parsed_url.query or ''

    # Sort headers
    signed_headers = ';'.join(sorted(headers.keys()))
    canonical_headers = '\n'.join([f"{k}:{v}" for k, v in sorted(headers.items())]) + '\n'

    # Create payload hash
    payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

    # Create canonical request
    canonical_request = f"{method}\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}"

    # Create string to sign
    algorithm = 'AWS4-HMAC-SHA256'
    timestamp = datetime.utcnow().strftime('%Y%m%dT%H%M%SZ')
    date_stamp = timestamp[:8]
    credential_scope = f"{date_stamp}/{AWS_REGION}/rekognition/aws4_request"
    string_to_sign = f"{algorithm}\n{timestamp}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()}"

    # Calculate signature
    def sign(key, msg):
        return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

    def getSignatureKey(key, dateStamp, regionName, serviceName):
        kDate = sign(('AWS4' + key).encode('utf-8'), dateStamp)
        kRegion = hmac.new(kDate, regionName.encode('utf-8'), hashlib.sha256).digest()
        kService = hmac.new(kRegion, serviceName.encode('utf-8'), hashlib.sha256).digest()
        kSigning = hmac.new(kService, b'aws4_request', hashlib.sha256).digest()
        return kSigning

    signing_key = getSignatureKey(AWS_SECRET_ACCESS_KEY, date_stamp, AWS_REGION, 'rekognition')
    signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

    # Add authorization header
    authorization_header = f"{algorithm} Credential={AWS_ACCESS_KEY_ID}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"
    headers['Authorization'] = authorization_header
    headers['X-Amz-Date'] = timestamp

    return headers

class AWSRekognitionFaceVerifier:
    """Handles face verification using AWS Rekognition with pre-recorded video files."""

    def __init__(
        self,
        video_file: str = VIDEO_FILE,
        collection_id: str = COLLECTION_ID,
        reference_face_id: str = REFERENCE_FACE_ID,
        aws_region: str = AWS_REGION,
        aws_access_key: str = AWS_ACCESS_KEY_ID,
        aws_secret_key: str = AWS_SECRET_ACCESS_KEY,
    ):
        self.video_file = video_file
        self.collection_id = collection_id
        self.reference_face_id = reference_face_id
        self.aws_region = aws_region
        self.aws_access_key = aws_access_key
        self.aws_secret_key = aws_secret_key

        # Initialize AWS Rekognition client
        self.rekognition_client = boto3.client(
            'rekognition',
            region_name=self.aws_region,
            aws_access_key_id=self.aws_access_key,
            aws_secret_access_key=self.aws_secret_key
        )

        # WebSocket connection
        self.websocket = None

        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        self.logger.info("AWS Rekognition Face Verifier initialized")

    def _extract_video_frames(self) -> list:
        """Extract frames from video file for processing."""
        if not os.path.exists(self.video_file):
            raise FileNotFoundError(f"Video file not found: {self.video_file}")

        self.logger.info(f"Extracting frames from video: {self.video_file}")

        cap = cv2.VideoCapture(self.video_file)
        frames = []
        frame_count = 0

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        self.logger.info(f"Video FPS: {fps}, Total frames: {total_frames}")

        # Extract frames at specified frame rate
        frame_interval = max(1, int(fps / FRAME_RATE))

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_count % frame_interval == 0:
                # Convert frame to JPEG bytes
                _, buffer = cv2.imencode('.jpg', frame)
                frame_bytes = buffer.tobytes()
                frames.append(frame_bytes)

            frame_count += 1

        cap.release()
        self.logger.info(f"Extracted {len(frames)} frames for processing")
        return frames

    async def _create_websocket_connection(self) -> bool:
        """Create WebSocket connection to AWS Rekognition streaming endpoint."""
        try:
            # Prepare WebSocket headers with AWS authentication
            headers = {
                'Host': f'rekognition.{self.aws_region}.amazonaws.com',
                'Content-Type': 'application/x-amz-json-1.1',
                'X-Amz-Target': 'RekognitionService.StartStreamProcessor'
            }

            # Sign the request
            signed_headers = _sign_aws_request(
                'GET',
                REKOGNITION_ENDPOINT,
                headers
            )

            self.logger.info("Connecting to AWS Rekognition WebSocket...")
            self.websocket = await websockets.connect(
                REKOGNITION_ENDPOINT,
                extra_headers=signed_headers
            )

            self.logger.info("WebSocket connection established")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create WebSocket connection: {str(e)}")
            return False

    async def _send_video_chunk(self, frame_data: bytes, frame_number: int) -> bool:
        """Send video frame chunk through WebSocket."""
        try:
            # Prepare event stream message
            headers = {
                ':message-type': 'event',
                ':event-type': 'VideoFrame',
                ':content-type': 'application/octet-stream'
            }

            # Create the message
            message = _construct_aws_event_stream_message(headers, frame_data)

            # Send through WebSocket
            await self.websocket.send(message)
            self.logger.debug(f"Sent frame {frame_number}, size: {len(frame_data)} bytes")

            return True

        except Exception as e:
            self.logger.error(f"Failed to send video chunk {frame_number}: {str(e)}")
            return False

    async def _receive_verification_result(self) -> Dict[str, Any]:
        """Receive and process verification result from AWS Rekognition."""
        try:
            self.logger.info("Waiting for verification result...")

            while True:
                # Receive message from WebSocket
                message = await self.websocket.recv()

                # Parse the event stream message
                result = self._parse_event_stream_message(message)

                if result.get('event_type') == 'FaceVerificationResult':
                    self.logger.info("Received face verification result")
                    return result
                elif result.get('event_type') == 'Error':
                    self.logger.error(f"Received error: {result.get('error_message')}")
                    return result

        except Exception as e:
            self.logger.error(f"Error receiving verification result: {str(e)}")
            return {"error": str(e)}

    def _parse_event_stream_message(self, message: bytes) -> Dict[str, Any]:
        """Parse AWS Event Stream message format."""
        try:
            # Parse the message structure
            if len(message) < 16:
                return {"error": "Message too short"}

            # Extract message components
            total_length = struct.unpack('!I', message[0:4])[0]
            header_length = struct.unpack('!I', message[4:8])[0]

            # Extract headers
            header_data = message[12:12+header_length]
            payload = message[12+header_length:-4]  # Exclude final CRC

            # Parse headers (simplified)
            headers = {}
            # In a real implementation, you'd parse the header format properly

            # Try to parse payload as JSON
            try:
                payload_data = json.loads(payload.decode('utf-8'))
                return {
                    "event_type": headers.get(':event-type', 'Unknown'),
                    "data": payload_data
                }
            except json.JSONDecodeError:
                return {
                    "event_type": headers.get(':event-type', 'Unknown'),
                    "raw_data": payload
                }

        except Exception as e:
            self.logger.error(f"Error parsing event stream message: {str(e)}")
            return {"error": str(e)}

    async def _prepare_curl_cffi_multipart(self, files, form_data=None):
        """Prepare multipart form data for curl_cffi client"""
        mp = CurlMime()
        try:
            # Add files
            for field_name, file_info in files.items():
                filename, file_content, content_type = file_info
                
                # Use the correct CurlMime API
                mp.addpart(name=field_name, content_type=content_type, filename=filename, data=file_content)

            # Add form fields
            if form_data:
                for key, value in form_data.items():
                    mp.addpart(name=key, data=str(value))

            return mp
        except Exception as e:
            self.logger.error(f"Error preparing curl_cffi multipart data: {str(e)}")
            return None

    async def _prepare_async_tls_multipart(self, files, form_data=None):
        """Prepare multipart form data for async_tls_client"""
        boundary = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(30))
        body = b''
        
        # Add files
        for field_name, file_info in files.items():
            filename, file_content, content_type = file_info
            body += f'--{boundary}\r\n'.encode()
            body += f'Content-Disposition: form-data; name="{field_name}"; filename="{filename}"\r\n'.encode()
            body += f'Content-Type: {content_type}\r\n\r\n'.encode()
            body += file_content
            body += b'\r\n'

        # Add form fields
        if form_data:
            for key, value in form_data.items():
                body += f'--{boundary}\r\n'.encode()
                body += f'Content-Disposition: form-data; name="{key}"\r\n\r\n'.encode()
                body += str(value).encode()
                body += b'\r\n'

        body += f'--{boundary}--\r\n'.encode()
        content_type = f'multipart/form-data; boundary={boundary}'
        
        return body, content_type

    def _process_response(self, response):
        try:
            result = response.json()
            # Get URL safely
            url = getattr(response, 'url', 'unknown')
            status_code = getattr(response, 'status_code', 0)
            self.logger.info(f"Response from {url}: {status_code}")
            self.logger.info(f"Full response JSON: {result}") # Log the full JSON response
            
            # CRITICAL: Extract session from response headers
            headers = getattr(response, 'headers', {})
            self.logger.info(f"Response headers: {headers}")
            
            # Look for session in various header formats
            session_token = None
            if 'x-vfs-session' in headers:
                session_token = headers['x-vfs-session']
                self.logger.info(f"Found session in x-vfs-session header: {session_token}")
            elif 'set-cookie' in headers:
                cookies = headers['set-cookie']
                self.logger.info(f"Found cookies: {cookies}")
                # Parse cookies to find session - handle both string and list formats
                if isinstance(cookies, list):
                    for cookie in cookies:
                        if 'vfs-session=' in cookie:
                            session_token = cookie.split('vfs-session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                        elif 'session=' in cookie.lower():
                            session_token = cookie.split('session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                else:
                    # Handle string format
                    if 'vfs-session=' in cookies:
                        session_token = cookies.split('vfs-session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
                    elif 'session=' in cookies.lower():
                        session_token = cookies.split('session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
            
            result["status_code"] = status_code
            if session_token:
                result["session_token"] = session_token
            return result
        except json.JSONDecodeError:
            # Get URL safely
            url = getattr(response, 'url', 'unknown')
            status_code = getattr(response, 'status_code', 0)
            self.logger.warning(f"Non-JSON response from {url}: {status_code}")
            
            # Even for non-JSON responses, try to extract session from headers
            headers = getattr(response, 'headers', {})
            self.logger.info(f"Non-JSON response headers: {headers}")
            
            session_token = None
            if 'x-vfs-session' in headers:
                session_token = headers['x-vfs-session']
                self.logger.info(f"Found session in x-vfs-session header: {session_token}")
            elif 'set-cookie' in headers:
                cookies = headers['set-cookie']
                self.logger.info(f"Found cookies: {cookies}")
                # Parse cookies to find session - handle both string and list formats
                if isinstance(cookies, list):
                    for cookie in cookies:
                        if 'vfs-session=' in cookie:
                            session_token = cookie.split('vfs-session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                        elif 'session=' in cookie.lower():
                            session_token = cookie.split('session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                else:
                    # Handle string format
                    if 'vfs-session=' in cookies:
                        session_token = cookies.split('vfs-session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
                    elif 'session=' in cookies.lower():
                        session_token = cookies.split('session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
            
            result = {"text_response": response.text, "status_code": status_code}
            if session_token:
                result["session_token"] = session_token
            return result
        except Exception as e:
            self.logger.error(f"Error processing response: {str(e)}")
            return None

    async def _get_fresh_vfs_session(self) -> str:
        """Get a fresh VFS session by making the initial request that triggers session generation."""
        self.logger.info("Getting fresh VFS session by triggering server session generation...")
        
        try:
            # Step 1: Make the initial request WITHOUT any session header to trigger server to generate one
            initial_headers = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "cache-control": "max-age=0",
                "cookie": self.browser_cookie,
                "dnt": "1",
                "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "user-agent": self.browser_user_agent
                # NO x-vfs-session header - let server generate one
            }
            
            initial_response = await self._send_request("GET", f"{self.base_url}/home", headers=initial_headers)
            self.logger.info(f"Initial request response: {initial_response.get('status_code') if initial_response else 'No response'}")
            
            # Extract session from initial response headers
            if initial_response and "session_token" in initial_response:
                session_token = initial_response["session_token"]
                self.logger.info(f"Extracted session from initial request: {session_token}")
                return session_token
            
            # Step 2: Try to extract session from cookies more thoroughly
            if initial_response and "text_response" in initial_response:
                # Parse the HTML response to look for session tokens in the page
                html_content = initial_response["text_response"]
                self.logger.info("Looking for session tokens in HTML response...")
                
                # Look for common session token patterns in HTML
                import re
                session_patterns = [
                    r'x-vfs-session["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'session["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'vfs-session["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'sessionId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'session_id["\']?\s*[:=]\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in session_patterns:
                    matches = re.findall(pattern, html_content)
                    if matches:
                        session_token = matches[0]
                        self.logger.info(f"Found session in HTML: {session_token}")
                        return session_token
            
            # Step 3: Try making a request to a different endpoint that might generate sessions
            # Try the main application endpoint
            app_headers = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "cookie": self.browser_cookie,
                "user-agent": self.browser_user_agent,
                "referer": f"{self.base_url}/home"
            }
            
            app_response = await self._send_request("GET", f"{self.base_url}/", headers=app_headers)
            if app_response and "session_token" in app_response:
                session_token = app_response["session_token"]
                self.logger.info(f"Extracted session from app endpoint: {session_token}")
                return session_token
            
            # Step 4: Try to generate a session by making a request with a UUID
            # Sometimes servers accept any valid UUID format as a session starter
            import uuid
            test_session = str(uuid.uuid4())
            test_headers = {
                "accept": "application/json",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "content-type": "application/json",
                "cookie": self.browser_cookie,
                "origin": "https://idnvui.vfsglobal.com",
                "referer": "https://idnvui.vfsglobal.com/home",
                "user-agent": self.browser_user_agent,
                "x-vfs-session": test_session
            }
            
            # Try a simple health check or status endpoint
            test_response = await self._send_request("GET", f"{self.base_url}/bff/health", headers=test_headers)
            if test_response and test_response.get("status_code") in [200, 401, 403]:
                # If we get a response (even 401/403), the session format might be accepted
                self.logger.info(f"Test session format accepted, using: {test_session}")
                return test_session
            
            # Step 5: Try the original approach with API calls but with better error handling
            api_headers = {
                "accept": "application/json",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "content-type": "application/json",
                "cookie": self.browser_cookie,
                "dnt": "1",
                "narrative_language": "en-US",
                "origin": "https://idnvui.vfsglobal.com",
                "priority": "u=1, i",
                "referer": "https://idnvui.vfsglobal.com/home",
                "screen_resolution": "1360x768",
                "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": self.browser_user_agent,
                "viewport": "1207x910"
            }
            
            # Try different endpoints that might not require a session
            endpoints_to_try = [
                f"{self.base_url}/bff/status",
                f"{self.base_url}/bff/health",
                f"{self.base_url}/api/status",
                f"{self.base_url}/api/health"
            ]
            
            for endpoint in endpoints_to_try:
                try:
                    endpoint_response = await self._send_request("GET", endpoint, headers=api_headers)
                    if endpoint_response and "session_token" in endpoint_response:
                        session_token = endpoint_response["session_token"]
                        self.logger.info(f"Extracted session from {endpoint}: {session_token}")
                        return session_token
                except Exception as e:
                    self.logger.debug(f"Failed to get session from {endpoint}: {str(e)}")
                    continue
            
            # Step 6: Last resort - try to use the browser cookie to extract session info
            # Parse the browser cookie to see if there's any session information
            if self.browser_cookie:
                cookie_parts = self.browser_cookie.split(';')
                for part in cookie_parts:
                    part = part.strip()
                    if 'session' in part.lower() or 'vfs' in part.lower():
                        if '=' in part:
                            key, value = part.split('=', 1)
                            if 'session' in key.lower():
                                self.logger.info(f"Found potential session in cookie: {value}")
                                return value
            
            # Step 7: Generate a new UUID as fallback
            new_session = str(uuid.uuid4())
            self.logger.info(f"Generating new session UUID: {new_session}")
            return new_session
                
        except Exception as e:
            self.logger.error(f"Error getting fresh VFS session: {str(e)}")
            # Generate a new UUID as fallback
            import uuid
            fallback_session = str(uuid.uuid4())
            self.logger.info(f"Using fallback session: {fallback_session}")
            return fallback_session

    async def _initialize_session(self) -> bool:
        """Initialize fresh VFS session by visiting the home page."""
        self.logger.info("Initializing fresh VFS session...")
        try:
            response = await self._send_request("GET", f"{self.base_url}/home")
            if response and response.get("status_code") == 200:
                self.logger.info("Successfully accessed home page")
                return True
            else:
                self.logger.error("Failed to access home page")
                return False
        except Exception as e:
            self.logger.error(f"Error initializing session: {str(e)}")
            return False

    async def verify_face_with_collection(self) -> Dict[str, Any]:
        """Verify face against a face in AWS Rekognition collection."""
        try:
            self.logger.info("Starting face verification with collection...")

            # Extract frames from video
            frames = self._extract_video_frames()
            if not frames:
                return {"success": False, "error": "No frames extracted from video"}

            # Use the first clear frame for verification
            frame_data = frames[0]

            # Call AWS Rekognition SearchFacesByImage
            response = self.rekognition_client.search_faces_by_image(
                CollectionId=self.collection_id,
                Image={'Bytes': frame_data},
                MaxFaces=1,
                FaceMatchThreshold=80.0
            )

            if response['FaceMatches']:
                match = response['FaceMatches'][0]
                confidence = match['Similarity']
                face_id = match['Face']['FaceId']

                self.logger.info(f"Face match found! Confidence: {confidence}%, Face ID: {face_id}")

                return {
                    "success": True,
                    "confidence": confidence,
                    "face_id": face_id,
                    "match": True
                }
            else:
                self.logger.info("No face match found in collection")
                return {
                    "success": True,
                    "confidence": 0,
                    "match": False
                }

        except Exception as e:
            self.logger.error(f"Face verification failed: {str(e)}")
            return {"success": False, "error": str(e)}

    async def stream_video_for_verification(self) -> Dict[str, Any]:
        """Stream video to AWS Rekognition for real-time face verification."""
        try:
            self.logger.info("Starting video streaming for face verification...")

            # Create WebSocket connection
            if not await self._create_websocket_connection():
                return {"success": False, "error": "Failed to create WebSocket connection"}

            # Extract frames from video
            frames = self._extract_video_frames()
            if not frames:
                return {"success": False, "error": "No frames extracted from video"}

            # Send frames through WebSocket
            for i, frame_data in enumerate(frames):
                if not await self._send_video_chunk(frame_data, i):
                    return {"success": False, "error": f"Failed to send frame {i}"}

                # Add small delay between frames to simulate real-time streaming
                await asyncio.sleep(1.0 / FRAME_RATE)

            # Send end-of-stream signal
            await self._send_end_of_stream()

            # Receive verification result
            result = await self._receive_verification_result()

            return result

        except Exception as e:
            self.logger.error(f"Video streaming failed: {str(e)}")
            return {"success": False, "error": str(e)}
        finally:
            if self.websocket:
                await self.websocket.close()

    async def _send_end_of_stream(self):
        """Send end-of-stream signal to AWS Rekognition."""
        try:
            headers = {
                ':message-type': 'event',
                ':event-type': 'EndOfStream',
                ':content-type': 'application/octet-stream'
            }

            message = _construct_aws_event_stream_message(headers, b'')
            await self.websocket.send(message)
            self.logger.info("Sent end-of-stream signal")

        except Exception as e:
            self.logger.error(f"Failed to send end-of-stream: {str(e)}")

    async def run_face_verification(self) -> Dict[str, Any]:
        """Run the complete face verification process."""
        self.logger.info("Starting AWS Rekognition face verification process...")

        try:
            # Method 1: Direct face verification with collection
            self.logger.info("Attempting direct face verification with collection...")
            collection_result = await self.verify_face_with_collection()

            if collection_result["success"]:
                self.logger.info("✅ Face verification completed successfully!")
                return collection_result

            # Method 2: If direct verification fails, try streaming approach
            self.logger.info("Direct verification failed, trying streaming approach...")
            streaming_result = await self.stream_video_for_verification()

            if streaming_result["success"]:
                self.logger.info("✅ Streaming face verification completed successfully!")
                return streaming_result
            else:
                self.logger.error("❌ Both verification methods failed")
                return streaming_result

        except Exception as e:
            self.logger.error(f"Face verification process failed: {str(e)}")
            return {"success": False, "error": str(e)}

    async def create_face_collection(self) -> bool:
        """Create a face collection in AWS Rekognition if it doesn't exist."""
        try:
            # Check if collection exists
            try:
                self.rekognition_client.describe_collection(CollectionId=self.collection_id)
                self.logger.info(f"Collection '{self.collection_id}' already exists")
                return True
            except self.rekognition_client.exceptions.ResourceNotFoundException:
                # Collection doesn't exist, create it
                self.logger.info(f"Creating new collection: {self.collection_id}")
                self.rekognition_client.create_collection(CollectionId=self.collection_id)
                self.logger.info("Collection created successfully")
                return True

        except Exception as e:
            self.logger.error(f"Failed to create collection: {str(e)}")
            return False

    async def add_reference_face(self, reference_image_path: str) -> bool:
        """Add a reference face to the collection."""
        try:
            if not os.path.exists(reference_image_path):
                self.logger.error(f"Reference image not found: {reference_image_path}")
                return False

            with open(reference_image_path, 'rb') as image_file:
                image_bytes = image_file.read()

            response = self.rekognition_client.index_faces(
                CollectionId=self.collection_id,
                Image={'Bytes': image_bytes},
                ExternalImageId=self.reference_face_id,
                MaxFaces=1,
                QualityFilter='AUTO'
            )

            if response['FaceRecords']:
                face_id = response['FaceRecords'][0]['Face']['FaceId']
                self.logger.info(f"Reference face added successfully. Face ID: {face_id}")
                return True
            else:
                self.logger.error("No face detected in reference image")
                return False

        except Exception as e:
            self.logger.error(f"Failed to add reference face: {str(e)}")
            return False

async def main():
    """Main function to run AWS Rekognition face verification."""
    # Check dependencies
    try:
        import crc32c
        import boto3
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please run: pip install boto3 crc32c")
        return

    # Check if video file exists
    if not os.path.exists(VIDEO_FILE):
        print(f"Video file not found: {VIDEO_FILE}")
        print("Please provide a video file for face verification.")
        return

    # Validate AWS credentials
    if AWS_ACCESS_KEY_ID == "YOUR_AWS_ACCESS_KEY" or AWS_SECRET_ACCESS_KEY == "YOUR_AWS_SECRET_KEY":
        print("⚠️  Please update AWS credentials in the configuration section")
        print("Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY with your actual AWS credentials")
        return

    print("🚀 Starting AWS Rekognition Face Verification")
    print(f"📹 Video file: {VIDEO_FILE}")
    print(f"🗂️  Collection ID: {COLLECTION_ID}")
    print(f"🆔 Reference Face ID: {REFERENCE_FACE_ID}")
    print("-" * 50)

    # Create and run face verifier
    verifier = AWSRekognitionFaceVerifier()

    try:
        # Create collection if needed
        print("📁 Setting up face collection...")
        if await verifier.create_face_collection():
            print("✅ Face collection ready")
        else:
            print("❌ Failed to setup face collection")
            return

        # Run face verification
        print("🔍 Starting face verification...")
        result = await verifier.run_face_verification()

        # Display results
        print("\n" + "="*50)
        print("🎯 FACE VERIFICATION RESULTS")
        print("="*50)

        if result["success"]:
            if result.get("match"):
                print(f"✅ FACE MATCH FOUND!")
                print(f"🎯 Confidence: {result.get('confidence', 0):.2f}%")
                print(f"🆔 Face ID: {result.get('face_id', 'N/A')}")
            else:
                print("❌ NO FACE MATCH FOUND")
                print("The person in the video does not match any face in the collection")
        else:
            print(f"❌ VERIFICATION FAILED: {result.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Error during verification: {str(e)}")

def create_sample_video():
    """Helper function to create a sample video file for testing."""
    print("📹 Creating sample video file...")

    # Create a simple test video using OpenCV
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(VIDEO_FILE, fourcc, 20.0, (640, 480))

    for i in range(100):  # 5 seconds at 20 FPS
        # Create a simple frame with text
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(frame, f'Test Frame {i}', (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        out.write(frame)

    out.release()
    print(f"✅ Sample video created: {VIDEO_FILE}")

if __name__ == "__main__":
    # Check if video file exists, if not offer to create a sample
    if not os.path.exists(VIDEO_FILE):
        response = input(f"Video file '{VIDEO_FILE}' not found. Create a sample video? (y/n): ")
        if response.lower() == 'y':
            try:
                import numpy as np
                create_sample_video()
            except ImportError:
                print("NumPy is required to create sample video. Please install: pip install numpy")
                sys.exit(1)
        else:
            print("Please provide a video file and try again.")
            sys.exit(1)

    asyncio.run(main())
