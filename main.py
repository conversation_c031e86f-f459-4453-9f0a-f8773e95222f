#!/usr/bin/env python3
"""
VFS Global Full Liveness Automation (Final Version)

This script automates the entire VFS Global identity verification process by
mimicking a real browser's headers, including a valid Cloudflare cookie.

This is a backend-only solution requiring no browser interaction once valid
headers are provided.
"""
import asyncio
import websockets
import cv2
import json
import sys
import time
import base64
import logging
import os
import struct
import requests
import secrets
import string
import uuid
from typing import Optional, Dict, Any, Literal
from urllib.parse import urlparse, parse_qs

from async_tls_client import AsyncSession as AsyncTLSSession
from curl_cffi import CurlMime
from curl_cffi.requests import AsyncSession as CurlAsyncSession

# --- Configuration ---
# VFS & Proxy Configuration
PROXY_STRING = "geo.iproyal.com:12321:letinnow:wearegoodinbook_country-cv_session-4l6aInzl_lifetime-4h"
VFS_SESSION = "606960d6-7fa7-4c13-935f-215e8a725072"  # Use the session from sample code
IDNFY_URL = "https://idnvui.vfsglobal.com/?token=%20XA/qVdSr1T5Z74oRihy9ao4x3hGsaLQguGdIcoWj1lemgCYBLtn9yC1nkHgW7CG9&language=en"

# Browser cookies from inspection data (exact values from browser)
BROWSER_COOKIE = "__cf_bm=u7NqiB4hOoWltLcHUHuLY8YWC6DLhKvS3JfbEfBp2JU-1755258046-*******-ZOrJSm8uKIlpLED2RcBV0GGzsuVXqqKDTTV.N_RBnJIzr3aQRqCW1V.avXeZJx6E0UKLumySw9CILSDbcAcBtvENpu2GTilXajP_4jKMpG0; _cfuvid=a4BTbb0odzMQ8e4vpFD4QqYBEPPiNGXGnROsB5S_ncY-1755258046861-*******-604800000; cf_clearance=rAIwwpjUCAMcjiywic2cKeI4uFEW45L9wlQl.nkQD9g-1755258052-*******-EXnYcsRLq71ebWRiQ3PYt5vB2cbU5sjF33D4z_XrHk7XWqWhZOsKWN4BoLN..5cw5Cfg5wfMpv_GUx33NNPnvID_jfjUgx_5kSP0oN2kERent_bPrSLOV8nx3QI9oPKY03PrQ2eRsTT3aE.HbFclvZXEK2aI5vVHysQIk3rtik597Ca7Wa9opFSupdLedVCcOF9STF4WHiMMa9Osq_9XeYt8bTaShIBSP.LAdSEftgc"

# Technical Configuration
HTTP_CLIENT = "async_tls_client"  # Switch back to async_tls_client
CAPSOLVER_API_KEY = "CAP-72BBF432EFADBDADA1E214973A43C35C9B71FF205D2D60C7143058AD378966BF"

# Media File Paths
VIDEO_FILE = "Face verification.mp4"
PASSPORT_COVER_PATH = "passport_cover.jpg"
PASSPORT_IMAGE_PATH = "passport_image.jpg"
FACE_IMAGE_PATH = "face_image.jpg"

# --- Headers to Mimic Browser (CRITICAL: UPDATE THESE) ---
# Paste the fresh values from your browser's inspector here
BROWSER_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
# --- End Configuration ---

def _construct_event_stream_message(headers: Dict[str, Any], payload: bytes) -> bytes:
    header_bytes = b''
    for name, value in headers.items():
        name_len = len(name)
        header_bytes += struct.pack('!B', name_len)
        header_bytes += name.encode('utf-8')
        header_bytes += struct.pack('!B', 7)
        value_bytes = value.encode('utf-8')
        header_bytes += struct.pack('!H', len(value_bytes))
        header_bytes += value_bytes
    header_length = len(header_bytes)
    total_length = 16 + header_length + len(payload)
    message = b''
    message += struct.pack('!I', total_length)
    message += struct.pack('!I', header_length)
    import crc32c
    preamble_crc = crc32c.crc32c(message)
    message += struct.pack('!I', preamble_crc)
    message += header_bytes
    message += payload
    message_crc = crc32c.crc32c(message)
    message += struct.pack('!I', message_crc)
    return message

class VFSAutomator:
    """Handles the entire VFS automation flow from authentication to video streaming."""

    def __init__(
        self,
        raw_proxy_string: str = None,
        proxy_url: str = None,
        vfs_session: str = VFS_SESSION,
        idnfy_url: str = IDNFY_URL,
        passport_cover_path: str = PASSPORT_COVER_PATH,
        passport_image_path: str = PASSPORT_IMAGE_PATH,
        face_image_path: str = FACE_IMAGE_PATH,
        http_client: str = HTTP_CLIENT,
    ):
        # Handle both old and new parameter names
        proxy_to_use = proxy_url if proxy_url else raw_proxy_string
        if not proxy_to_use:
            raise ValueError("Either proxy_url or raw_proxy_string must be provided")
            
        self.proxy_url = self._format_proxy(proxy_to_use)
        self.raw_proxy_string = proxy_to_use  # Store original for Capsolver
        self.http_client = http_client
        self.base_url = "https://idnvui.vfsglobal.com"
        self.idnv_url = f"{self.base_url}/bff"
        self.token_id = self._extract_token(idnfy_url) if idnfy_url else None
        self.session = None
        
        # Set session and cookie attributes
        self.vfs_session = vfs_session
        self.browser_cookie = BROWSER_COOKIE
        self.browser_user_agent = BROWSER_USER_AGENT
        
        # Set file paths
        self.passport_cover_path = passport_cover_path
        self.passport_image_path = passport_image_path
        self.face_image_path = face_image_path
        
        # Build headers
        self.headers = self._build_initial_headers()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def _build_initial_headers(self) -> Dict[str, str]:
        # Headers from sample code to ensure compatibility
        return {
            "accept": "application/json",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
            "connection": "keep-alive",
            "origin": "https://idnvui.vfsglobal.com",
            "priority": "u=1, i",
            "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "referer": f"{self.base_url}/home",
            "viewport": "694x730",
            "screen_resolution": "1536x864",
            "narrative_language": "en-US",
            "x-vfs-session": self.vfs_session,
            "cookie": self.browser_cookie
        }

    def _format_proxy(self, proxy_string):
        parts = proxy_string.split(':')
        if len(parts) == 4:
            return f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"
        else:
            raise ValueError("Invalid proxy format")

    def _extract_token(self, url):
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        return query_params.get('token', [None])[0]

    async def create_session(self):
        """Create HTTP session with proxy configuration."""
        if self.http_client == "curl_cffi":
            self.session = CurlAsyncSession()
        elif self.http_client == "async_tls_client":
            self.session = AsyncTLSSession()
        else:
            raise ValueError(f"Unsupported HTTP client: {self.http_client}")

    async def _send_request(
        self,
        method: str,
        url: str,
        headers: Dict = None,
        json_data: Dict = None,
        form_data: Dict = None,
        files: Dict = None,
        params: Dict = None,
        timeout: int = 60,
    ) -> dict[str, Any] | None:
        if headers is None:
            headers = self.headers.copy()

        request_kwargs = {"headers": headers, "proxy": self.proxy_url}
        if params:
            request_kwargs["params"] = params

        if json_data:
            request_kwargs["json"] = json_data

        try:
            # Create request kwargs based on client type
            # Add form data if no files (otherwise it will be handled in multipart)
            if form_data and not files:
                request_kwargs["data"] = form_data

            # Handle multipart form data
            if files:
                if self.http_client == "curl_cffi":
                    mp = await self._prepare_curl_cffi_multipart(files, form_data)
                    request_kwargs["files"] = mp
                elif self.http_client == "async_tls_client":
                    body, content_type = await self._prepare_async_tls_multipart(files, form_data)
                    request_kwargs["data"] = body
                    headers["content-type"] = content_type

            # Send the request with the appropriate client
            if self.http_client == "curl_cffi":
                # curl_cffi uses methods like session.get, session.post
                action = getattr(self.session, method.lower())
                response = await action(url, timeout=timeout, **request_kwargs)
            else:
                # async_tls_client uses methods like session.get, session.post
                action = getattr(self.session, method.lower())
                response = await action(url, **request_kwargs)

            return self._process_response(response)

        except Exception as e:
            self.logger.error(f"Request failed: {str(e)}")
            return None

    async def _prepare_curl_cffi_multipart(self, files, form_data=None):
        """Prepare multipart form data for curl_cffi client"""
        mp = CurlMime()
        try:
            # Add files
            for field_name, file_info in files.items():
                filename, file_content, content_type = file_info
                
                # Use the correct CurlMime API
                mp.addpart(name=field_name, content_type=content_type, filename=filename, data=file_content)

            # Add form fields
            if form_data:
                for key, value in form_data.items():
                    mp.addpart(name=key, data=str(value))

            return mp
        except Exception as e:
            self.logger.error(f"Error preparing curl_cffi multipart data: {str(e)}")
            return None

    async def _prepare_async_tls_multipart(self, files, form_data=None):
        """Prepare multipart form data for async_tls_client"""
        boundary = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(30))
        body = b''
        
        # Add files
        for field_name, file_info in files.items():
            filename, file_content, content_type = file_info
            body += f'--{boundary}\r\n'.encode()
            body += f'Content-Disposition: form-data; name="{field_name}"; filename="{filename}"\r\n'.encode()
            body += f'Content-Type: {content_type}\r\n\r\n'.encode()
            body += file_content
            body += b'\r\n'

        # Add form fields
        if form_data:
            for key, value in form_data.items():
                body += f'--{boundary}\r\n'.encode()
                body += f'Content-Disposition: form-data; name="{key}"\r\n\r\n'.encode()
                body += str(value).encode()
                body += b'\r\n'

        body += f'--{boundary}--\r\n'.encode()
        content_type = f'multipart/form-data; boundary={boundary}'
        
        return body, content_type

    def _process_response(self, response):
        try:
            result = response.json()
            # Get URL safely
            url = getattr(response, 'url', 'unknown')
            status_code = getattr(response, 'status_code', 0)
            self.logger.info(f"Response from {url}: {status_code}")
            self.logger.info(f"Full response JSON: {result}") # Log the full JSON response
            
            # CRITICAL: Extract session from response headers
            headers = getattr(response, 'headers', {})
            self.logger.info(f"Response headers: {headers}")
            
            # Look for session in various header formats
            session_token = None
            if 'x-vfs-session' in headers:
                session_token = headers['x-vfs-session']
                self.logger.info(f"Found session in x-vfs-session header: {session_token}")
            elif 'set-cookie' in headers:
                cookies = headers['set-cookie']
                self.logger.info(f"Found cookies: {cookies}")
                # Parse cookies to find session - handle both string and list formats
                if isinstance(cookies, list):
                    for cookie in cookies:
                        if 'vfs-session=' in cookie:
                            session_token = cookie.split('vfs-session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                        elif 'session=' in cookie.lower():
                            session_token = cookie.split('session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                else:
                    # Handle string format
                    if 'vfs-session=' in cookies:
                        session_token = cookies.split('vfs-session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
                    elif 'session=' in cookies.lower():
                        session_token = cookies.split('session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
            
            result["status_code"] = status_code
            if session_token:
                result["session_token"] = session_token
            return result
        except json.JSONDecodeError:
            # Get URL safely
            url = getattr(response, 'url', 'unknown')
            status_code = getattr(response, 'status_code', 0)
            self.logger.warning(f"Non-JSON response from {url}: {status_code}")
            
            # Even for non-JSON responses, try to extract session from headers
            headers = getattr(response, 'headers', {})
            self.logger.info(f"Non-JSON response headers: {headers}")
            
            session_token = None
            if 'x-vfs-session' in headers:
                session_token = headers['x-vfs-session']
                self.logger.info(f"Found session in x-vfs-session header: {session_token}")
            elif 'set-cookie' in headers:
                cookies = headers['set-cookie']
                self.logger.info(f"Found cookies: {cookies}")
                # Parse cookies to find session - handle both string and list formats
                if isinstance(cookies, list):
                    for cookie in cookies:
                        if 'vfs-session=' in cookie:
                            session_token = cookie.split('vfs-session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                        elif 'session=' in cookie.lower():
                            session_token = cookie.split('session=')[1].split(';')[0]
                            self.logger.info(f"Found session in cookie list: {session_token}")
                            break
                else:
                    # Handle string format
                    if 'vfs-session=' in cookies:
                        session_token = cookies.split('vfs-session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
                    elif 'session=' in cookies.lower():
                        session_token = cookies.split('session=')[1].split(';')[0]
                        self.logger.info(f"Found session in cookie string: {session_token}")
            
            result = {"text_response": response.text, "status_code": status_code}
            if session_token:
                result["session_token"] = session_token
            return result
        except Exception as e:
            self.logger.error(f"Error processing response: {str(e)}")
            return None

    async def _get_fresh_vfs_session(self) -> str:
        """Get a fresh VFS session by making the initial request that triggers session generation."""
        self.logger.info("Getting fresh VFS session by triggering server session generation...")
        
        try:
            # Step 1: Make the initial request WITHOUT any session header to trigger server to generate one
            initial_headers = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "cache-control": "max-age=0",
                "cookie": self.browser_cookie,
                "dnt": "1",
                "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "user-agent": self.browser_user_agent
                # NO x-vfs-session header - let server generate one
            }
            
            initial_response = await self._send_request("GET", f"{self.base_url}/home", headers=initial_headers)
            self.logger.info(f"Initial request response: {initial_response.get('status_code') if initial_response else 'No response'}")
            
            # Extract session from initial response headers
            if initial_response and "session_token" in initial_response:
                session_token = initial_response["session_token"]
                self.logger.info(f"Extracted session from initial request: {session_token}")
                return session_token
            
            # Step 2: Try to extract session from cookies more thoroughly
            if initial_response and "text_response" in initial_response:
                # Parse the HTML response to look for session tokens in the page
                html_content = initial_response["text_response"]
                self.logger.info("Looking for session tokens in HTML response...")
                
                # Look for common session token patterns in HTML
                import re
                session_patterns = [
                    r'x-vfs-session["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'session["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'vfs-session["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'sessionId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'session_id["\']?\s*[:=]\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in session_patterns:
                    matches = re.findall(pattern, html_content)
                    if matches:
                        session_token = matches[0]
                        self.logger.info(f"Found session in HTML: {session_token}")
                        return session_token
            
            # Step 3: Try making a request to a different endpoint that might generate sessions
            # Try the main application endpoint
            app_headers = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "cookie": self.browser_cookie,
                "user-agent": self.browser_user_agent,
                "referer": f"{self.base_url}/home"
            }
            
            app_response = await self._send_request("GET", f"{self.base_url}/", headers=app_headers)
            if app_response and "session_token" in app_response:
                session_token = app_response["session_token"]
                self.logger.info(f"Extracted session from app endpoint: {session_token}")
                return session_token
            
            # Step 4: Try to generate a session by making a request with a UUID
            # Sometimes servers accept any valid UUID format as a session starter
            import uuid
            test_session = str(uuid.uuid4())
            test_headers = {
                "accept": "application/json",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "content-type": "application/json",
                "cookie": self.browser_cookie,
                "origin": "https://idnvui.vfsglobal.com",
                "referer": "https://idnvui.vfsglobal.com/home",
                "user-agent": self.browser_user_agent,
                "x-vfs-session": test_session
            }
            
            # Try a simple health check or status endpoint
            test_response = await self._send_request("GET", f"{self.base_url}/bff/health", headers=test_headers)
            if test_response and test_response.get("status_code") in [200, 401, 403]:
                # If we get a response (even 401/403), the session format might be accepted
                self.logger.info(f"Test session format accepted, using: {test_session}")
                return test_session
            
            # Step 5: Try the original approach with API calls but with better error handling
            api_headers = {
                "accept": "application/json",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "en-US,en;q=0.9",
                "content-type": "application/json",
                "cookie": self.browser_cookie,
                "dnt": "1",
                "narrative_language": "en-US",
                "origin": "https://idnvui.vfsglobal.com",
                "priority": "u=1, i",
                "referer": "https://idnvui.vfsglobal.com/home",
                "screen_resolution": "1360x768",
                "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": self.browser_user_agent,
                "viewport": "1207x910"
            }
            
            # Try different endpoints that might not require a session
            endpoints_to_try = [
                f"{self.base_url}/bff/status",
                f"{self.base_url}/bff/health",
                f"{self.base_url}/api/status",
                f"{self.base_url}/api/health"
            ]
            
            for endpoint in endpoints_to_try:
                try:
                    endpoint_response = await self._send_request("GET", endpoint, headers=api_headers)
                    if endpoint_response and "session_token" in endpoint_response:
                        session_token = endpoint_response["session_token"]
                        self.logger.info(f"Extracted session from {endpoint}: {session_token}")
                        return session_token
                except Exception as e:
                    self.logger.debug(f"Failed to get session from {endpoint}: {str(e)}")
                    continue
            
            # Step 6: Last resort - try to use the browser cookie to extract session info
            # Parse the browser cookie to see if there's any session information
            if self.browser_cookie:
                cookie_parts = self.browser_cookie.split(';')
                for part in cookie_parts:
                    part = part.strip()
                    if 'session' in part.lower() or 'vfs' in part.lower():
                        if '=' in part:
                            key, value = part.split('=', 1)
                            if 'session' in key.lower():
                                self.logger.info(f"Found potential session in cookie: {value}")
                                return value
            
            # Step 7: Generate a new UUID as fallback
            new_session = str(uuid.uuid4())
            self.logger.info(f"Generating new session UUID: {new_session}")
            return new_session
                
        except Exception as e:
            self.logger.error(f"Error getting fresh VFS session: {str(e)}")
            # Generate a new UUID as fallback
            import uuid
            fallback_session = str(uuid.uuid4())
            self.logger.info(f"Using fallback session: {fallback_session}")
            return fallback_session

    async def _initialize_session(self) -> bool:
        """Initialize fresh VFS session by visiting the home page."""
        self.logger.info("Initializing fresh VFS session...")
        try:
            response = await self._send_request("GET", f"{self.base_url}/home")
            if response and response.get("status_code") == 200:
                self.logger.info("Successfully accessed home page")
                return True
            else:
                self.logger.error("Failed to access home page")
                return False
        except Exception as e:
            self.logger.error(f"Error initializing session: {str(e)}")
            return False

    async def detect_passport(self) -> bool:
        """Detect passport in image using the exact same approach as sample code."""
        url = f"{self.idnv_url}/detect-passport"

        with open(self.passport_cover_path, "rb") as f:
            base64_image_data = base64.b64encode(f.read()).decode("utf-8")
        
        headers = {**self.headers, "content-type": "application/json"}
        payload = {"image": str(base64_image_data)}

        self.logger.info("Detecting passport...")
        response = await self._send_request("POST", url, headers=headers, json_data=payload)

        if "detail" in response:
            self.logger.error(f"Error: {response['detail']}")
            return False

        is_passport = response.get("passport", False)
        is_terminated = response.get("terminate", True)

        if not is_terminated and is_passport:
            confidence = response.get("Details", [{}])[0].get("Confidence", 0)
            self.logger.info(f"Passport detected with confidence: {confidence}")
            return True
        return False

    async def initiate_passport_validation(self) -> bool:
        """Validate passport document using the exact same approach as sample code."""
        url = f"{self.idnv_url}/initiate-passport-validation"

        self.logger.info("Validating passport...")

        # Read file content
        with open(self.passport_image_path, "rb") as f:
            file_content = f.read()

        # Prepare files dictionary for the request helper
        files = {"file": ("passport.jpg", file_content, "image/jpeg")}

        response = await self._send_request("POST", url, files=files)
        self.logger.info(f"Passport validation response: {response}")

        # Check for errors
        error_subcode = response.get("Data", {}).get("error_subcode")
        if error_subcode:
            self.logger.info(error_subcode)

        # Extract document ID if validation was successful
        document_id = response.get("Data", {}).get("document_id")
        passport_valid = response.get("Data", {}).get("passport_valid") == "Yes"

        if passport_valid and document_id:
            self.logger.info(f"Passport validation successful. Document ID: {document_id}")
            return True
        return False

    async def initiate_face_validation(self) -> bool:
        """Compare face images using the exact same approach as sample code."""
        url = f"{self.idnv_url}/initiate-face-validation"

        self.logger.info("Validating face...")

        # Read both image files
        with open(self.face_image_path, "rb") as f1:
            source_content = f1.read()

        with open(self.passport_image_path, "rb") as f2:
            target_content = f2.read()

        # Prepare files for the request helper
        files = {
            "source_image": ("source_file.jpg", source_content, "image/jpeg"),
            "target_image": ("captured_file.jpg", target_content, "image/jpeg"),
        }

        response = await self._send_request("POST", url, files=files)

        similarity = response.get("Data", {}).get("similarity", 0)
        is_match = response.get("Data", {}).get("is_match", False)

        if is_match:
            self.logger.info(f"Face validation successful. Similarity: {similarity}")
            return True
        return False

    async def initiate_callback(self) -> bool:
        """Finalize verification process using the exact same approach as sample code."""
        url = f"{self.idnv_url}/initiate-callback"

        headers = {
            **self.headers,
            "reason": "",
        }

        response = await self._send_request("GET", url, headers=headers)

        if response.get("status_code") in {200, "200"}:
            return True
        return False

    async def run_api_verification(self) -> bool:
        """Run the complete API verification process."""
        self.logger.info("Starting API verification process...")
        
        # Step 1: Detect passport
        if not await self.detect_passport():
            self.logger.error("Passport detection failed.")
            return False
        
        # Step 2: Initiate passport validation
        if not await self.initiate_passport_validation():
            self.logger.error("Passport validation failed.")
            return False
        
        # Step 3: Initiate face validation
        if not await self.initiate_face_validation():
            self.logger.error("Face validation failed.")
            return False
        
        # Step 4: Initiate callback
        if not await self.initiate_callback():
            self.logger.error("Callback failed.")
            return False
        
        self.logger.info("API Verification Overall Status: Success")
        return True

    async def run_full_flow(self):
        """Orchestrates the entire automation process."""
        await self.create_session()
        
        try:
            # IP check to confirm proxy is working
            ip_response = await self._send_request("GET", "https://ipinfo.io/json")
            self.logger.info(f"Using IP: {ip_response.get('ip', 'unknown') if ip_response else 'unknown'}")

            # Get fresh VFS session automatically
            fresh_session = await self._get_fresh_vfs_session()
            if fresh_session:
                self.logger.info(f"Successfully got fresh VFS session: {fresh_session}")
                # Update the session in headers
                self.headers["x-vfs-session"] = fresh_session
            else:
                self.logger.warning("Failed to get fresh session, using original")

            # Initialize fresh session
            session_initialized = await self._initialize_session()
            if not session_initialized:
                self.logger.error("Failed to initialize session. Halting process.")
                return

            # Run API verification
            api_success = await self.run_api_verification()
            if not api_success:
                self.logger.error("API verification failed. Halting process.")
                return

            self.logger.info("Full automation completed successfully!")

        except Exception as e:
            self.logger.error(f"Error in full flow: {str(e)}")
        finally:
            if self.session:
                await self.session.close()

async def main():
    """Main function to run the VFS automation."""
    # Check dependencies
    try:
        import crc32c
    except ImportError:
        print("Missing dependency 'crc32c'. Please run: pip install crc32c")
        return

    # Check required files
    required_files = [PASSPORT_COVER_PATH, PASSPORT_IMAGE_PATH, FACE_IMAGE_PATH]
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"Required file not found: {file_path}")
            return

    # Create and run automator
    automator = VFSAutomator(raw_proxy_string=PROXY_STRING)
    await automator.run_full_flow()

if __name__ == "__main__":
    asyncio.run(main())
